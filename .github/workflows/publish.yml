name: Publish to <PERSON>y<PERSON>

on:
  release:
    types: [published]

jobs:
  build-and-publish:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.x'
    
    - name: Install build dependencies
      run: |
        python -m pip install --upgrade pip
        pip install build twine
    
    - name: Build package
      run: python -m build
    
    - name: Check distribution
      run: twine check dist/*
    
    - name: Upload to Test PyPI
      env:
        TWINE_USERNAME: __token__
        TWINE_PASSWORD: ${{ secrets.TEST_PYPI_API_TOKEN }}
        TWINE_REPOSITORY: testpypi
      run: twine upload --skip-existing dist/*

    # Test installation from both repositories
    - name: Test installation from TestPyPI
      run: |
        python -m pip install --index-url https://test.pypi.org/simple/ --no-deps elm
        elm --version

    - name: Test installation from PyPI
      if: github.event_name == 'release' && github.event.action == 'published'
      run: |
        python -m pip install elm
        elm --version