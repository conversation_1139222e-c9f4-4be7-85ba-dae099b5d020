import subprocess
import json
import sys
from typing import List, Dict, Optional


def is_docker_installed() -> bool:
    """
    Check if Docker is installed and accessible.
    Returns True if Docker is installed and running, False otherwise.
    """
    try:
        result = subprocess.run(
            ["docker", "--version"],
            capture_output=True,
            text=True,
            timeout=10
        )
        return result.returncode == 0
    except (subprocess.TimeoutExpired, FileNotFoundError):
        return False


def is_docker_daemon_running() -> bool:
    """
    Check if Docker daemon is running. Status code will be 1 if <PERSON><PERSON> is not running.
    Returns True if daemon is accessible, False otherwise.
    """
    try:
        result = subprocess.run(
            ["docker", "info"],
            capture_output=True,
            text=True,
            timeout=10
        )
        return result.returncode == 0
    except (subprocess.TimeoutExpired, FileNotFoundError):
        return False


def get_container_info(container_name: str) -> Optional[Dict]:
    """
    Get detailed information about a specific container by name.
    Returns container info dict if found, None otherwise.
    """
    try:
        result = subprocess.run(
            ["docker", "inspect", container_name],
            capture_output=True,
            text=True,
            timeout=10
        )
        if result.returncode == 0:
            return json.loads(result.stdout)[0]
        return None
    except (subprocess.TimeoutExpired, FileNotFoundError, json.JSONDecodeError):
        return None


def container_exists(container_name: str) -> bool:
    """
    Check if a container with the given name exists (running or stopped).
    Returns True if container exists, False otherwise.
    """
    try:
        result = subprocess.run(
            ["docker", "ps", "-a", "--filter", f"name=^{container_name}$", "--format", "{{.Names}}"],
            capture_output=True,
            text=True,
            timeout=10
        )
        return result.returncode == 0 and container_name in result.stdout.strip()
    except (subprocess.TimeoutExpired, FileNotFoundError):
        return False


def is_container_running(container_name: str) -> bool:
    """
    Check if a container with the given name is currently running.
    Returns True if container is running, False otherwise.
    """
    try:
        result = subprocess.run(
            ["docker", "ps", "--filter", f"name=^{container_name}$", "--format", "{{.Names}}"],
            capture_output=True,
            text=True,
            timeout=10
        )
        return result.returncode == 0 and container_name in result.stdout.strip()
    except (subprocess.TimeoutExpired, FileNotFoundError):
        return False
    
def check_specific_containers(container_names: List[str]) -> Dict[str, Dict[str, bool]]:
    """
    Check the status of multiple specific containers.
    Returns dict with container names as keys and status info as values.
    """
    results = {}
    for name in container_names:
        results[name] = {
            'exists': container_exists(name),
            'running': is_container_running(name)
        }
    return results


# Example usage and test functions
def run_docker_tests():
    """
    Example test function that demonstrates all the checks.
    """
    print("=== Docker Installation Check ===")
    
    # Check if Docker is installed
    if not is_docker_installed():
        print("❌ Docker is not installed or not in PATH")
        return False
    else:
        print("✅ Docker is installed")
    
    # Check if Docker daemon is running
    if not is_docker_daemon_running():
        print("❌ Docker daemon is not running")
        return False
    else:
        print("✅ Docker daemon is running")
    
    print("\n=== Container Status Check ===")
    
    # Check specific containers
    target_containers = ['ELM_TOOL_postgresql', 'ELM_TOOL_mysql']
    container_status = check_specific_containers(target_containers)
    
    for name, status in container_status.items():
        if status['exists']:
            running_status = "running" if status['running'] else "stopped"
            print(f"✅ Container '{name}' exists ({running_status})")
        else:
            print(f"❌ Container '{name}' not found")
    
    return True


# Test class for use with unittest
import unittest

class DockerTestCase(unittest.TestCase):
    """
    Test case class for Docker-related tests.
    """
    
    def setUp(self):
        """Set up test fixtures before each test method."""
        pass
    
    def test_docker_installation(self):
        """Test if Docker is properly installed."""
        self.assertTrue(is_docker_installed(), "Docker should be installed")
        self.assertTrue(is_docker_daemon_running(), "Docker daemon should be running")
    
    def test_postgresql_container(self):
        """Test if PostgreSQL container exists."""
        container_info = get_container_info('ELM_TOOL_postgresql')
        if container_info:
            self.assertIsNotNone(container_info, "PostgreSQL container should exist")
            print(f"PostgreSQL container status: {container_info['State']['Status']}")
        else:
            self.skipTest("PostgreSQL container not found - skipping test")
    
    def test_mysql_container(self):
        """Test if MySQL container exists."""
        container_info = get_container_info('ELM_TOOL_mysql')
        if container_info:
            self.assertIsNotNone(container_info, "MySQL container should exist")
            print(f"MySQL container status: {container_info['State']['Status']}")
        else:
            self.skipTest("MySQL container not found - skipping test")
    
    def test_container_existence(self):
        """Test container existence for required containers."""
        required_containers = ['ELM_TOOL_postgresql', 'ELM_TOOL_mysql']
        results = check_specific_containers(required_containers)
        
        for name, status in results.items():
            with self.subTest(container=name):
                if status['exists']:
                    print(f"✅ {name} exists and is {'running' if status['running'] else 'stopped'}")
                else:
                    print(f"⚠️  {name} does not exist")


if __name__ == "__main__":
    # Run the example tests
    print("Running Docker tests...")
    run_docker_tests()
    
    print("\n" + "="*50)
    print("Running unittest tests...")
    
    # Run unittest tests
    unittest.main(verbosity=2)